/* Navigation Item Container */
.nav-item {
  margin: 0.25rem 0.75rem;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item.active {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
}

/* Navigation Button */
.nav-button {
  width: 100%;
  padding: 0.875rem;
  background: transparent;
  border: none;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #475569;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.nav-button:hover {
  background: rgba(59, 130, 246, 0.08);
  color: #3b82f6;
  transform: translateX(2px);
}

.nav-button:active {
  transform: translateX(1px) scale(0.98);
}

.nav-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  background: rgba(59, 130, 246, 0.08);
}

/* Collapsed state button */
.nav-button.collapsed {
  padding: 0.75rem;
  justify-content: center;
  min-height: 48px;
}

.nav-button.collapsed:hover {
  transform: scale(1.1);
}

/* Active state */
.nav-item.active .nav-button {
  color: #1e40af;
  font-weight: 600;
  background: rgba(59, 130, 246, 0.1);
}

.nav-item.active .nav-button:hover {
  background: rgba(59, 130, 246, 0.15);
}

/* Navigation Icon */
.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 1.1rem;
  transition: all 0.2s ease;
}

.nav-button:hover .nav-icon {
  transform: scale(1.1);
}

.nav-item.active .nav-icon {
  color: #2563eb;
}

/* Navigation Label */
.nav-label {
  flex: 1;
  text-align: left;
  font-size: 0.9rem;
  line-height: 1.4;
  transition: all 0.2s ease;
}

/* Chevron Icon */
.nav-chevron {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.chevron-icon {
  font-size: 0.75rem;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chevron-icon.expanded {
  transform: rotate(0deg);
}

.nav-button:hover .nav-chevron {
  opacity: 1;
}

/* Navigation Content (Accordion) */
.nav-content {
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(248, 250, 252, 0.8);
  border-top: 1px solid rgba(226, 232, 240, 0.5);
}

.nav-content.collapsed {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

.nav-content.expanded {
  max-height: 1000px;
  opacity: 1;
  transform: translateY(0);
}

.nav-content-inner {
  padding: 1rem;
  animation: fadeInUp 0.3s ease-out;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ripple effect */
.nav-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(59, 130, 246, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
  z-index: 0;
}

.nav-button:active::before {
  width: 100px;
  height: 100px;
}

.nav-button > * {
  position: relative;
  z-index: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-item {
    margin: 0.2rem 0.5rem;
  }
  
  .nav-button {
    padding: 0.75rem;
    gap: 0.5rem;
  }
  
  .nav-button.collapsed {
    padding: 0.6rem;
    min-height: 44px;
  }
  
  .nav-label {
    font-size: 0.85rem;
  }
  
  .nav-icon {
    width: 18px;
    height: 18px;
    font-size: 1rem;
  }
}

@media (max-width: 576px) {
  .nav-item {
    margin: 0.15rem 0.4rem;
  }
  
  .nav-button {
    padding: 0.6rem;
  }
  
  .nav-button.collapsed {
    padding: 0.5rem;
    min-height: 40px;
  }
  
  .nav-content-inner {
    padding: 0.75rem;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .nav-button {
    border: 1px solid transparent;
  }
  
  .nav-button:hover,
  .nav-button:focus {
    border-color: #000000;
    background: #ffffff;
  }
  
  .nav-item.active .nav-button {
    border-color: #000000;
    background: #ffffff;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .nav-button,
  .nav-icon,
  .nav-chevron,
  .chevron-icon,
  .nav-content,
  .nav-content-inner {
    transition: none;
    animation: none;
  }
  
  .nav-button::before {
    display: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .nav-item.active {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  }
  
  .nav-button {
    color: #cbd5e1;
  }
  
  .nav-button:hover {
    background: rgba(96, 165, 250, 0.15);
    color: #60a5fa;
  }
  
  .nav-item.active .nav-button {
    color: #dbeafe;
    background: rgba(96, 165, 250, 0.2);
  }
  
  .nav-content {
    background: rgba(15, 23, 42, 0.8);
    border-top-color: rgba(51, 65, 85, 0.5);
  }
}
