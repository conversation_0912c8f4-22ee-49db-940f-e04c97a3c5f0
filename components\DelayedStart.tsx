"use client";

import { useState, useEffect } from "react";

export default function DelayedStart({ matchData }: { matchData: any }) {
  // --- Interval Table Data ---
  type IntervalRow = {
    label: string;
    overs: number;
    length: number;
    end: string;
    interval: number;
    secondStart: string;
    secondEnd: string;
    bowler: number;
    powerplay: number;
    lost: number;
  };

  // Powerplay lookup table
  const powerplayTable: Record<string, number> = {
    "5": 1.3,
    "6": 1.5,
    "7": 2.1,
    "8": 2.2,
    "9": 2.4,
    "10": 3.0,
    "11": 3.2,
    "12": 3.4,
    "13": 3.5,
    "14": 4.1,
    "15": 4.3,
    "16": 4.5,
    "17": 5.1,
    "18": 5.2,
    "19": 5.4,
    "20": 6.0,
  };
  function getPowerplayOvers(overs: number) {
    return powerplayTable[String(overs)] || 6.0;
  }

  // State and calculation logic
  const [A, setA] = useState(170);
  const B = 0;
  const [C, setC] = useState(0);
  const D = matchData?.matchType === "Playoff Match" ? 60 : 30;
  const [E, setE] = useState(0);

  const resetForm = () => {
    setA(170);
    setC(0);
    setE(0);
  };

  useEffect(() => {
    const diff = C - D;
    if (C <= D) setE(0);
    else if (diff > 10) setE(10);
    else if (diff > 0) setE(diff);
  }, [C, D]);

  // Calculation logic
  const F = C - (D + E);
  const N = Math.min(E > 10 ? 10 : 20 - E, 20);
  const G = A - F;
  const H = G / 4.25;
  const I = Math.min(Math.ceil(H / 2), 20);
  const J = I > 9 ? Math.ceil(I / 5) : 2;
  const L = Math.ceil(I * 4.25);

  function timeStrToMinutes(time: string) {
    if (!time) return 0;
    const [h, m] = time.split(":").map(Number);
    return h * 60 + m;
  }
  function minutesToTimeStr(mins: number) {
    const h = Math.floor(mins / 60);
    const m = mins % 60;
    return `${String(h).padStart(2, "0")}:${String(m).padStart(2, "0")}`;
  }

  const startTimeStr = matchData?.matchStartTime || "--:--";
  const K = timeStrToMinutes(startTimeStr);
  const firstInningsCommence = minutesToTimeStr(K + C);
  const firstInningsEnd = minutesToTimeStr(K + C + (L - B));
  const secondInningsStart = minutesToTimeStr(K + C + (L - B) + N);
  const secondInningsEnd = minutesToTimeStr(K + C + (L - B) + N + L);
  const powerplayOvers = getPowerplayOvers(I);
  const showCriticalMsg = I < 5;

  // Calculate intervals for the table
  const intervals: IntervalRow[] = (() => {
    const arr: IntervalRow[] = [];
    let i = 0;
    let minOversReached = false;
    const maxIntervals = 24;
    while (!minOversReached && i < maxIntervals) {
      const lost = i * 5;
      const Cx = lost;
      let Ex = 0;
      const Dx = D;
      const diffx = Cx - Dx;
      if (Cx <= Dx) Ex = 0;
      else if (diffx > 10) Ex = 10;
      else if (diffx > 0) Ex = diffx;
      const Fx = Cx - (Dx + Ex);
      const Gx = A - Fx;
      const Hx = Gx / 4.25;
      const Ix = Math.min(Math.ceil(Hx / 2), 20);
      if (Ix <= 5) minOversReached = true;
      const Jx = Ix > 9 ? Math.ceil(Ix / 5) : 2;
      const Lx = Math.ceil(Ix * 4.25);
      const Kx = timeStrToMinutes(startTimeStr);
      const firstInningsCommenceX = minutesToTimeStr(Kx + Cx);
      const Mx = Kx + Cx + (Lx - B);
      const firstInningsEndX = minutesToTimeStr(Mx);
      const Nx = Math.min(Ex > 10 ? 10 : 20 - Ex, 20);
      const Ox = Mx + Nx;
      const secondInningsStartX = minutesToTimeStr(Ox);
      const Px = Ox + Lx;
      const secondInningsEndX = minutesToTimeStr(Px);
      const powerplayX = getPowerplayOvers(Ix);
      arr.push({
        label: firstInningsCommenceX,
        overs: Ix,
        length: Lx,
        end: firstInningsEndX,
        interval: Nx,
        secondStart: secondInningsStartX,
        secondEnd: secondInningsEndX,
        bowler: Jx,
        powerplay: powerplayX,
        lost,
      });
      i++;
    }
    return arr;
  })();

  const rows = [
    { name: "Start Time", key: "label" },
    { name: "Maximum Overs Per Team", key: "overs" },
    { name: "Length of Innings", key: "length" },
    { name: "Rescheduled First Innings Cessation Time", key: "end" },
    { name: "Length of Interval", key: "interval" },
    { name: "Second Innings Commencement Time", key: "secondStart" },
    { name: "Rescheduled Second Innings Cessation Time", key: "secondEnd" },
    { name: "Maximum Overs Per Bowler", key: "bowler" },
    { name: "Powerplay", key: "powerplay" },
  ];

  // Match Information card style for perfect consistency
  const cardClass =
    "bg-white rounded-xl shadow-sm border border-gray-200 p-6 flex flex-col justify-between";

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2 flex items-center gap-2">
          <i className="ri-pause-circle-line text-2xl" /> Delayed Start
        </h1>
        <p className="text-gray-600">Enter delay details to calculate revised hours of play.</p>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Input Card */}
        <div className={cardClass + " min-h-[600px]"}>
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Net playing time available at start of the match (A)
              </label>
              <input
                type="number"
                min={1}
                max={170}
                value={A}
                onChange={(e) =>
                  setA(Math.max(1, Math.min(170, Number(e.target.value))))
                }
                className="w-full border rounded-lg px-3 py-2"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Time innings in progress (B)
              </label>
              <input
                type="number"
                value={B}
                disabled
                className="w-full border rounded-lg px-3 py-2 bg-gray-100"
                placeholder="0"
                title="Time innings in progress is always 0"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Playing time lost (C)
              </label>
              <input
                type="number"
                min={0}
                max={210}
                value={C}
                onChange={(e) =>
                  setC(Math.max(0, Math.min(210, Number(e.target.value))))
                }
                className="w-full border rounded-lg px-3 py-2"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Extra time available (D)
              </label>
              <input
                type="number"
                value={D}
                disabled
                className="w-full border rounded-lg px-3 py-2 bg-gray-100"
                placeholder={String(D)}
                title="Extra time available is set by match type"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Time made up from reduced interval (E)
              </label>
              <input
                type="number"
                min={0}
                max={20}
                value={E}
                onChange={(e) =>
                  setE(Math.max(0, Math.min(20, Number(e.target.value))))
                }
                className="w-full border rounded-lg px-3 py-2"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                First Innings to commence
              </label>
              <input
                type="text"
                value={firstInningsCommence}
                disabled
                className="w-full border rounded-lg px-3 py-2 bg-gray-100 font-semibold text-blue-900"
                placeholder="First Innings to commence"
                title="Dynamically calculated start time"
              />
            </div>
          </div>
          <div className="mt-6 flex justify-end gap-2">
            <button
              onClick={resetForm}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              Reset
            </button>
          </div>
        </div>
        {/* Output Card */}
        <div className={cardClass + " min-h-[600px]"}>
          <div className="flex flex-col h-full">
            <div className="flex items-center gap-2 mb-2">
              <i className="ri-calendar-check-line text-xl text-purple-900" />
              <h3 className="text-xl font-bold text-purple-900">
                Rescheduled Hours of Play
              </h3>
            </div>
            {showCriticalMsg && (
              <div className="mb-2 text-red-700 font-semibold">
                Minimum overs to constitute a match is 5 Overs
              </div>
            )}
            <div className="space-y-2 mt-2 flex-1">
              <div className="flex justify-between">
                <span className="font-semibold">Start Time:</span>{" "}
                <span>{firstInningsCommence}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">Maximum overs per team:</span>{" "}
                <span>{I}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">Length of innings:</span>{" "}
                <span>{L}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">
                  Rescheduled First Innings Cessation Time:
                </span>{" "}
                <span>{firstInningsEnd}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">Length of interval:</span>{" "}
                <span>{N}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">
                  Second Innings Commencement Time:
                </span>{" "}
                <span>{secondInningsStart}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">
                  Rescheduled Second Innings Cessation Time:
                </span>{" "}
                <span>{secondInningsEnd}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">Maximum overs per bowler:</span>{" "}
                <span>{J}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">Powerplay:</span>{" "}
                <span>{powerplayOvers}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Interval Table Card */}
      <div className="col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-6 mt-8">
        <div className="flex items-center gap-2 mb-2">
          <i className="ri-table-line text-xl text-green-700" />
          <h3 className="text-lg font-bold text-green-700">
            Reschedule HoP - Delayed Start: 5 Minutes Interval
          </h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full text-xs">
            <thead>
              <tr className="bg-gray-100">
                <th className="px-2 py-1 font-semibold text-left">Parameter</th>
                {intervals.map((interval, idx) => (
                  <th key={idx} className="px-2 py-1 font-semibold text-center">
                    {interval.lost === 0
                      ? minutesToTimeStr(timeStrToMinutes(startTimeStr))
                      : minutesToTimeStr(
                          timeStrToMinutes(startTimeStr) + interval.lost
                        )}
                    <br />
                    <span className="text-xs text-gray-500">
                      ({interval.lost} min lost)
                    </span>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {rows.map((row, rIdx) => (
                <tr key={rIdx} className="border-b">
                  <td className="px-2 py-1 font-semibold text-left bg-gray-50">
                    {row.name}
                  </td>
                  {intervals.map((interval, idx) => (
                    <td key={idx} className="px-2 py-1 text-center">
                      {interval[row.key as keyof IntervalRow]}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
