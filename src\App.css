.app {
  display: flex;
  height: 100vh;
  width: 100vw;
  background-color: #f8fafc;
  overflow: hidden;
  position: relative;
}

/* Skip link for accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000000;
  color: #ffffff;
  padding: 8px 12px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 10000;
  font-weight: 600;
  font-size: 0.875rem;
  transition: top 0.3s ease;
}

.skip-link:focus {
  top: 6px;
  outline: 2px solid #ffffff;
  outline-offset: 2px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app {
    flex-direction: column;
  }
}

/* Global transitions */
* {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus management for accessibility */
.app:focus-within {
  outline: none;
}

/* Prevent text selection during animations */
.app.animating * {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  pointer-events: none;
}

.app.animating .sidebar {
  pointer-events: auto;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .app {
    background-color: #ffffff;
    border: 2px solid #000000;
  }

  .skip-link {
    background: #000000;
    border: 2px solid #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .skip-link {
    transition: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .app {
    background-color: #0f172a;
  }
}
