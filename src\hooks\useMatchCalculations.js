import { useMemo } from 'react';

/**
 * Custom hook for cricket match time calculations
 * @param {string} startTime - Start time in HH:MM format
 * @returns {object} - Calculated match schedule
 */
export const useMatchCalculations = (startTime) => {
  const scheduledHours = useMemo(() => {
    if (!startTime) {
      return {
        firstInnings: '-- : -- - -- : --',
        intervalStart: '-- : --',
        intervalEnd: '-- : --',
        secondInnings: '-- : -- - -- : --',
        matchEnd: '-- : --'
      };
    }

    try {
      const [hours, minutes] = startTime.split(':').map(Number);
      
      // Validate time format
      if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
        throw new Error('Invalid time format');
      }

      const startDate = new Date();
      startDate.setHours(hours, minutes, 0, 0);

      // First innings: 90 minutes
      const firstInningsEnd = new Date(startDate.getTime() + 90 * 60000);
      
      // Interval: 20 minutes
      const intervalEnd = new Date(firstInningsEnd.getTime() + 20 * 60000);
      
      // Second innings: 90 minutes
      const secondInningsEnd = new Date(intervalEnd.getTime() + 90 * 60000);

      const formatTime = (date) => {
        return date.toTimeString().slice(0, 5);
      };

      const formatTimeRange = (start, end) => {
        return `${formatTime(start)} - ${formatTime(end)}`;
      };

      return {
        firstInnings: formatTimeRange(startDate, firstInningsEnd),
        intervalStart: formatTime(firstInningsEnd),
        intervalEnd: formatTime(intervalEnd),
        secondInnings: formatTimeRange(intervalEnd, secondInningsEnd),
        matchEnd: formatTime(secondInningsEnd),
        totalDuration: Math.round((secondInningsEnd - startDate) / 60000) // in minutes
      };
    } catch (error) {
      console.error('Error calculating match schedule:', error);
      return {
        firstInnings: 'Invalid Time',
        intervalStart: 'Invalid Time',
        intervalEnd: 'Invalid Time',
        secondInnings: 'Invalid Time',
        matchEnd: 'Invalid Time'
      };
    }
  }, [startTime]);

  return scheduledHours;
};

/**
 * Custom hook for validating cricket match data
 * @param {object} matchData - Match data to validate
 * @returns {object} - Validation results
 */
export const useMatchValidation = (matchData) => {
  const validation = useMemo(() => {
    const errors = {};
    let isValid = true;

    // Validate teams
    if (!matchData.teams || !matchData.teams.trim()) {
      errors.teams = 'Teams are required';
      isValid = false;
    } else if (matchData.teams.trim().length < 3) {
      errors.teams = 'Team names must be at least 3 characters long';
      isValid = false;
    } else if (matchData.teams.trim().length > 100) {
      errors.teams = 'Team names must be less than 100 characters';
      isValid = false;
    }

    // Validate timeouts
    if (typeof matchData.timeouts !== 'number' || matchData.timeouts < 0 || matchData.timeouts > 4) {
      errors.timeouts = 'Timeouts must be between 0 and 4';
      isValid = false;
    }

    // Validate start time
    if (!matchData.startTime) {
      errors.startTime = 'Start time is required';
      isValid = false;
    } else {
      const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (!timeRegex.test(matchData.startTime)) {
        errors.startTime = 'Invalid time format (HH:MM)';
        isValid = false;
      }
    }

    return {
      errors,
      isValid,
      hasErrors: Object.keys(errors).length > 0
    };
  }, [matchData]);

  return validation;
};
