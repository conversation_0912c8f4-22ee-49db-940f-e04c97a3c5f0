{"name": "cricket-match-management", "version": "1.0.0", "description": "A React-based cricket match management application with collapsible sidebar", "main": "index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "dev": "react-scripts start"}, "keywords": ["cricket", "match", "management", "react", "sidebar", "sports"], "author": "Cricket Match Management Team", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/react-fontawesome": "^0.2.0", "react-tooltip": "^5.21.1", "framer-motion": "^10.16.4", "styled-components": "^6.0.7", "react-bootstrap": "^2.8.0", "bootstrap": "^5.3.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "homepage": "."}