/* Sidebar Container */
.sidebar {
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  border-right: 1px solid #e2e8f0;
  height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar.expanded {
  width: 400px;
  min-width: 400px;
}

.sidebar.collapsed {
  width: 70px;
  min-width: 70px;
}

/* Header */
.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  background: #ffffff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 600;
  color: #1e293b;
  font-size: 1.1rem;
}

.header-icon {
  color: #3b82f6;
  font-size: 1.2rem;
}

.toggle-btn {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  transition: all 0.2s ease;
  cursor: pointer;
}

.toggle-btn:hover {
  background: #e2e8f0;
  color: #3b82f6;
  transform: scale(1.05);
}

.toggle-btn:active {
  transform: scale(0.95);
}

/* Navigation */
.sidebar-nav {
  padding: 0.5rem 0;
  flex: 1;
}

/* Footer */
.sidebar-footer {
  padding: 1rem;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
  margin-top: auto;
}

.footer-content {
  text-align: center;
}

/* Content Panel (for collapsed state) */
.content-panel {
  flex: 1;
  background: #ffffff;
  border-right: 1px solid #e2e8f0;
  height: 100vh;
  overflow-y: auto;
  animation: slideIn 0.3s ease-out;
}

.content-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  background: #ffffff;
  position: sticky;
  top: 0;
  z-index: 5;
}

.content-header h4 {
  margin: 0;
  color: #1e293b;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.content-body {
  padding: 2rem;
  background: #f8fafc;
  min-height: calc(100vh - 80px);
}

/* Placeholder content */
.placeholder-content {
  padding: 2rem;
  text-align: center;
  color: #64748b;
  background: #f1f5f9;
  border-radius: 12px;
  border: 2px dashed #cbd5e1;
  font-style: italic;
}

/* Custom Tooltip Styles */
.custom-tooltip {
  background: #1e293b !important;
  color: #ffffff !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  z-index: 9999 !important;
}

.custom-tooltip::after {
  border-right-color: #1e293b !important;
}

/* Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar.expanded {
    width: 100vw;
    position: fixed;
    z-index: 1050;
  }
  
  .sidebar.collapsed {
    width: 60px;
    min-width: 60px;
  }
  
  .content-panel {
    display: none;
  }
  
  .header-title {
    font-size: 1rem;
  }
  
  .toggle-btn {
    width: 32px;
    height: 32px;
  }
}

@media (max-width: 576px) {
  .sidebar.collapsed {
    width: 50px;
    min-width: 50px;
  }
  
  .sidebar-header {
    padding: 0.75rem;
  }
  
  .toggle-btn {
    width: 28px;
    height: 28px;
  }
}

/* Accessibility improvements */
.sidebar:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: -2px;
}

.toggle-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .sidebar {
    border-right: 2px solid #000000;
  }
  
  .toggle-btn {
    border: 2px solid #000000;
  }
  
  .header-title {
    color: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .sidebar,
  .toggle-btn,
  .content-panel {
    transition: none;
    animation: none;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  .sidebar {
    background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
    border-right-color: #334155;
  }
  
  .sidebar-header {
    background: #1e293b;
    border-bottom-color: #334155;
  }
  
  .header-title {
    color: #f1f5f9;
  }
  
  .toggle-btn {
    background: #334155;
    border-color: #475569;
    color: #cbd5e1;
  }
  
  .toggle-btn:hover {
    background: #475569;
    color: #60a5fa;
  }
  
  .content-panel {
    background: #0f172a;
    border-right-color: #334155;
  }
  
  .content-header {
    background: #1e293b;
    border-bottom-color: #334155;
  }
  
  .content-header h4 {
    color: #f1f5f9;
  }
  
  .content-body {
    background: #0f172a;
  }
}
