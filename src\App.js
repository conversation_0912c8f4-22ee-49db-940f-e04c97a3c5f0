import React, { useState, useEffect } from "react";
import Sidebar from "./components/Sidebar/Sidebar";
import ResponsiveLayout from "./components/Layout/ResponsiveLayout";
import "./App.css";

function App() {
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Auto-collapse on mobile
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth <= 768) {
        setIsCollapsed(true);
      }
    };

    handleResize(); // Check on mount
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <ResponsiveLayout>
      <div className="app">
        {/* Skip link for accessibility */}
        <a href="#main-content" className="skip-link">
          Skip to main content
        </a>

        <Sidebar isCollapsed={isCollapsed} onToggle={toggleSidebar} />

        {/* Main content area for screen readers */}
        <div id="main-content" className="sr-only">
          Cricket Match Management Application
        </div>
      </div>
    </ResponsiveLayout>
  );
}

export default App;
