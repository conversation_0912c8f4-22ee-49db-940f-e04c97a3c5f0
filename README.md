# Cricket Match Management Application (React.js)

A modern, responsive React.js web application for managing cricket match information with a collapsible sidebar design.

## 🚀 Features

### 🏏 Match Information Management

- **Teams Input**: Required text field with real-time validation
- **Time-outs Counter**: Interactive counter with increment/decrement buttons (0-4 range)
- **Match Start Time**: Time input with automatic schedule calculations
- **Scheduled Hours Display**: Auto-calculated match timeline with real-time updates
- **Data Persistence**: Form data automatically saved to localStorage

### 📱 Collapsible Sidebar Design

- **Toggle Functionality**: Smooth collapse/expand animations
- **Icon-Only Mode**: Collapsed state shows only icons with tooltips
- **Full Navigation**: Expanded state shows icons and labels
- **Responsive Behavior**: Auto-collapse on mobile devices
- **Accessibility**: Full keyboard navigation and screen reader support

### 🎯 Navigation Tabs

1. **Match Information** 🏏 - Complete form with calculations
2. **Cut-off Time** ⏰ - Time management features
3. **Delayed Start** ⏸️ - Delay handling
4. **Interruption: 1st Innings** ⚠️ - First innings management
5. **Interruption: 2nd Innings** ⚠️ - Second innings management

### ⚡ Real-time Calculations

- **First Innings**: Start time + 90 minutes
- **Interval Length**: Fixed 20 minutes
- **Second Innings**: First innings end + interval + 90 minutes
- **Edge Case Handling**: Midnight crossover and invalid time formats

### ✅ Advanced Form Validation

- Real-time input validation with custom hooks
- Visual feedback with success/error states
- Accessible error messages with ARIA labels
- Form persistence across browser sessions

## 🛠️ Technical Stack

- **React 18.2+**: Modern React with hooks and functional components
- **Custom Hooks**: useLocalStorage, useMatchCalculations, useMatchValidation
- **Font Awesome**: Professional icon library
- **React Tooltip**: Enhanced tooltip functionality
- **Framer Motion**: Smooth animations and transitions
- **Bootstrap 5**: Responsive framework and components
- **Styled Components**: Component-scoped styling

## 📁 Project Structure

```
src/
├── components/
│   ├── Layout/
│   │   ├── ResponsiveLayout.js    # Responsive wrapper component
│   │   └── ResponsiveLayout.css
│   ├── Sidebar/
│   │   ├── Sidebar.js             # Main sidebar component
│   │   ├── Sidebar.css
│   │   ├── NavigationItem.js      # Individual nav items
│   │   └── NavigationItem.css
│   └── MatchInformation/
│       ├── MatchInformation.js    # Match form component
│       └── MatchInformation.css
├── hooks/
│   ├── useLocalStorage.js         # localStorage persistence
│   └── useMatchCalculations.js    # Match calculations & validation
├── App.js                         # Main application component
├── App.css
├── index.js                       # React entry point
└── index.css
```

## 🚀 Getting Started

### Prerequisites

- Node.js 16+ and npm

### Installation

1. **Install dependencies**:

   ```bash
   npm install
   ```

   Or run the installation script:

   ```bash
   install.bat
   ```

2. **Start the development server**:

   ```bash
   npm start
   ```

3. **Open your browser** to `http://localhost:3000`

## 📖 Usage

### Sidebar Navigation

- **Toggle Button**: Click the arrow button to collapse/expand sidebar
- **Collapsed Mode**: Hover over icons to see tooltips with labels
- **Expanded Mode**: Full navigation with icons and text labels
- **Keyboard Navigation**: Use Tab and Enter keys to navigate

### Match Information Tab

1. **Enter Teams**: Type team names (e.g., "AB vs CD") - required field
2. **Set Time-outs**: Use +/- buttons to adjust count (0-4 range)
3. **Select Start Time**: Choose match start time - calculations update automatically
4. **View Schedule**: Real-time calculated match timeline displays below
5. **Data Persistence**: Form data automatically saves to localStorage

### Responsive Behavior

- **Desktop**: Full sidebar with content panel when collapsed
- **Tablet**: Sidebar adapts to smaller screen
- **Mobile**: Auto-collapse sidebar, full-screen navigation

## 🌐 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile browsers (iOS Safari, Chrome Mobile)

## ♿ Accessibility Features

- **ARIA Labels**: Comprehensive labeling for screen readers
- **Keyboard Navigation**: Full keyboard support with focus management
- **Skip Links**: Quick navigation to main content
- **High Contrast**: Support for high contrast mode
- **Reduced Motion**: Respects user motion preferences
- **Screen Reader**: Compatible with NVDA, JAWS, VoiceOver
- **Focus Indicators**: Clear visual focus indicators

## 🎨 Customization

### Component Styling

Modify component CSS files to customize:

- **Colors**: Primary color scheme (#3b82f6)
- **Typography**: Font families and sizes
- **Spacing**: Padding and margins
- **Animations**: Transition durations and easing

### Adding New Features

Extend functionality by:

- Creating new components in `src/components/`
- Adding custom hooks in `src/hooks/`
- Implementing new navigation tabs
- Adding API integrations

## 🚀 Future Enhancements

- [ ] **PWA Support**: Offline functionality and app installation
- [ ] **API Integration**: Backend data synchronization
- [ ] **Export Features**: PDF/Excel export functionality
- [ ] **Multi-language**: Internationalization support
- [ ] **Dark Mode**: User-selectable theme
- [ ] **Advanced Calculations**: DLS method integration
- [ ] **Match History**: Previous matches tracking
- [ ] **Team Management**: Player and team database

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

- **Issues**: Create an issue on GitHub
- **Discussions**: Use GitHub Discussions for questions
- **Email**: Contact the development team

---

**Built with ❤️ for cricket match management using React.js**
