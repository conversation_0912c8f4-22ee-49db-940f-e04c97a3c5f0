# Cricket Match Management Application

A responsive web application for managing cricket match information, built with modern web technologies.

## Features

### 🏏 Match Information Management
- **Teams Input**: Required text field for entering team names
- **Time-outs Counter**: Interactive counter with increment/decrement buttons (0-4 range)
- **Match Start Time**: HTML5 time input with real-time calculations
- **Scheduled Hours Display**: Auto-calculated match timeline

### 📱 Responsive Design
- Mobile-first responsive layout
- Bootstrap 5 framework integration
- Smooth animations and transitions
- Accessibility-compliant interface

### 🎯 Key Functionality

#### Sidebar Navigation
- 5 expandable accordion tabs:
  1. **Match Information** - Main form and calculations
  2. **Cut-off Time** - Time management features
  3. **Delayed Start** - Delay handling
  4. **Interruption: 1st Innings** - First innings management
  5. **Interruption: 2nd Innings** - Second innings management

#### Real-time Calculations
- **First Innings**: Start time + 90 minutes
- **Interval Length**: Fixed 20 minutes
- **Second Innings**: First innings end + interval + 90 minutes
- Handles time calculations including midnight crossover

#### Form Validation
- Required field validation for teams
- Real-time input validation
- Bootstrap validation styling
- Accessible error messages

## Technical Stack

- **HTML5**: Semantic markup with accessibility features
- **CSS3**: Modern styling with Bootstrap 5
- **JavaScript ES6+**: Modular class-based architecture
- **Bootstrap 5**: Responsive framework and components
- **Font Awesome**: Icon library

## File Structure

```
├── index.html          # Main HTML structure
├── styles.css          # Custom CSS styles
├── script.js           # JavaScript functionality
└── README.md           # Documentation
```

## Getting Started

1. **Clone or download** the project files
2. **Open** `index.html` in a modern web browser
3. **Start using** the application immediately - no build process required

## Usage

### Match Information Tab

1. **Enter Teams**: Type team names (e.g., "AB vs CD")
2. **Set Time-outs**: Use +/- buttons to adjust count (0-4)
3. **Select Start Time**: Choose match start time using time picker
4. **View Schedule**: Automatically calculated match timeline appears below

### Navigation

- Click accordion headers to expand/collapse sections
- Use "View Details" links to switch between tabs
- All interactions are keyboard accessible

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Accessibility Features

- ARIA labels and descriptions
- Keyboard navigation support
- Screen reader compatibility
- High contrast color scheme
- Focus indicators

## Customization

### Styling
Modify `styles.css` to customize:
- Color scheme (primary color: #1976d2)
- Typography and spacing
- Component styling
- Responsive breakpoints

### Functionality
Extend `script.js` to add:
- Additional form fields
- Complex calculations
- Data persistence
- API integrations

## Future Enhancements

- [ ] Data persistence (localStorage/database)
- [ ] Export/import functionality
- [ ] Advanced interruption calculations
- [ ] Multi-language support
- [ ] Print-friendly layouts
- [ ] Offline functionality (PWA)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For questions or issues, please create an issue in the repository or contact the development team.

---

**Built with ❤️ for cricket match management**
