import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown, faChevronRight } from '@fortawesome/free-solid-svg-icons';
import './NavigationItem.css';

const NavigationItem = ({ 
  item, 
  isCollapsed, 
  isActive, 
  isExpanded, 
  onToggle 
}) => {
  const handleClick = () => {
    onToggle();
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      onToggle();
    }
  };

  return (
    <div className={`nav-item ${isActive ? 'active' : ''}`}>
      {/* Navigation Button */}
      <button
        className={`nav-button ${isCollapsed ? 'collapsed' : 'expanded'}`}
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        aria-expanded={isExpanded}
        aria-controls={`content-${item.id}`}
        data-tooltip-id={isCollapsed ? "nav-tooltip" : undefined}
        data-tooltip-content={isCollapsed ? item.label : undefined}
        tabIndex={0}
      >
        <div className="nav-icon">
          <FontAwesomeIcon icon={item.icon} />
        </div>
        
        {!isCollapsed && (
          <>
            <span className="nav-label">{item.label}</span>
            <div className="nav-chevron">
              <FontAwesomeIcon 
                icon={isExpanded ? faChevronDown : faChevronRight}
                className={`chevron-icon ${isExpanded ? 'expanded' : ''}`}
              />
            </div>
          </>
        )}
      </button>

      {/* Accordion Content */}
      {!isCollapsed && (
        <div 
          className={`nav-content ${isExpanded ? 'expanded' : 'collapsed'}`}
          id={`content-${item.id}`}
          aria-hidden={!isExpanded}
        >
          <div className="nav-content-inner">
            {isExpanded && item.component && <item.component />}
          </div>
        </div>
      )}
    </div>
  );
};

export default NavigationItem;
