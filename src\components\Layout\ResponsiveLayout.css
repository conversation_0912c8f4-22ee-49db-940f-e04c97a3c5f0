/* Responsive Layout Container */
.responsive-layout {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

/* Desktop Layout */
.responsive-layout.desktop {
  display: flex;
}

/* Tablet Layout */
.responsive-layout.tablet {
  display: flex;
}

/* Mobile Layout */
.responsive-layout.mobile {
  display: flex;
  flex-direction: column;
}

/* Screen size specific adjustments */
@media (max-width: 768px) {
  .responsive-layout {
    height: 100vh;
    overflow-y: auto;
  }
}

@media (max-width: 576px) {
  .responsive-layout {
    height: 100vh;
    overflow-y: auto;
  }
}

/* Accessibility improvements */
.responsive-layout:focus-within {
  outline: none;
}

/* Skip link for accessibility */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000000;
  color: #ffffff;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 10000;
  transition: top 0.3s ease;
}

.skip-link:focus {
  top: 6px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .responsive-layout {
    border: 2px solid #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .responsive-layout * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
