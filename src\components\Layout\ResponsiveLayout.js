import React, { useState, useEffect } from 'react';
import './ResponsiveLayout.css';

const ResponsiveLayout = ({ children }) => {
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [screenSize, setScreenSize] = useState('desktop');

  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth;
      
      if (width <= 576) {
        setScreenSize('mobile');
        setIsMobile(true);
        setIsTablet(false);
      } else if (width <= 768) {
        setScreenSize('tablet');
        setIsMobile(false);
        setIsTablet(true);
      } else {
        setScreenSize('desktop');
        setIsMobile(false);
        setIsTablet(false);
      }
    };

    // Check on mount
    checkScreenSize();

    // Add event listener
    window.addEventListener('resize', checkScreenSize);

    // Cleanup
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  return (
    <div 
      className={`responsive-layout ${screenSize}`}
      data-screen-size={screenSize}
      role="main"
      aria-label="Cricket Match Management Application"
    >
      {React.cloneElement(children, { 
        isMobile, 
        isTablet, 
        screenSize 
      })}
    </div>
  );
};

export default ResponsiveLayout;
