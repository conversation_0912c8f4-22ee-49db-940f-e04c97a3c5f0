import React, { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPlus,
  faMinus,
  faClock,
  faUsers,
  faStopwatch,
} from "@fortawesome/free-solid-svg-icons";
import { useLocalStorage } from "../../hooks/useLocalStorage";
import {
  useMatchCalculations,
  useMatchValidation,
} from "../../hooks/useMatchCalculations";
import "./MatchInformation.css";

const MatchInformation = () => {
  // Use localStorage to persist form data
  const [formData, setFormData] = useLocalStorage("cricketMatchData", {
    teams: "",
    timeouts: 0,
    startTime: "15:20",
  });

  // Use custom hooks for calculations and validation
  const scheduledHours = useMatchCalculations(formData.startTime);
  const { errors, isValid } = useMatchValidation(formData);

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const updateTimeouts = (change) => {
    const newValue = formData.timeouts + change;
    if (newValue >= 0 && newValue <= 4) {
      handleInputChange("timeouts", newValue);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (isValid) {
      console.log("Form submitted:", formData);
      // Here you would typically send data to an API
      alert("Match information saved successfully!");
    }
  };

  return (
    <div className="match-information">
      <form onSubmit={handleSubmit} className="match-form">
        {/* Teams Input */}
        <div className="form-group">
          <label htmlFor="teams" className="form-label">
            <FontAwesomeIcon icon={faUsers} className="label-icon" />
            Teams for the match
            <span className="required">*</span>
          </label>
          <input
            type="text"
            id="teams"
            className={`form-control ${
              errors.teams ? "is-invalid" : formData.teams ? "is-valid" : ""
            }`}
            value={formData.teams}
            onChange={(e) => handleInputChange("teams", e.target.value)}
            placeholder="e.g., AB vs CD"
            aria-describedby="teams-error"
          />
          {errors.teams && (
            <div id="teams-error" className="error-message">
              {errors.teams}
            </div>
          )}
        </div>

        {/* Time Outs Counter */}
        <div className="form-group">
          <label className="form-label">
            <FontAwesomeIcon icon={faStopwatch} className="label-icon" />
            Number of Time Outs taken:
          </label>
          <div className="timeout-counter">
            <button
              type="button"
              className="counter-btn decrement"
              onClick={() => updateTimeouts(-1)}
              disabled={formData.timeouts === 0}
              aria-label="Decrease timeouts"
            >
              <FontAwesomeIcon icon={faMinus} />
            </button>
            <input
              type="number"
              className="counter-input"
              value={formData.timeouts}
              readOnly
              min="0"
              max="4"
              aria-label="Number of timeouts"
            />
            <button
              type="button"
              className="counter-btn increment"
              onClick={() => updateTimeouts(1)}
              disabled={formData.timeouts === 4}
              aria-label="Increase timeouts"
            >
              <FontAwesomeIcon icon={faPlus} />
            </button>
          </div>
          <small className="form-text">Range: 0-4 timeouts</small>
        </div>

        {/* Match Start Time */}
        <div className="form-group">
          <label htmlFor="startTime" className="form-label">
            <FontAwesomeIcon icon={faClock} className="label-icon" />
            Start Time of the match:
          </label>
          <input
            type="time"
            id="startTime"
            className={`form-control time-input ${
              errors.startTime ? "is-invalid" : "is-valid"
            }`}
            value={formData.startTime}
            onChange={(e) => handleInputChange("startTime", e.target.value)}
            aria-describedby="startTime-error"
          />
          {errors.startTime && (
            <div id="startTime-error" className="error-message">
              {errors.startTime}
            </div>
          )}
        </div>

        {/* Scheduled Hours Display */}
        <div className="scheduled-hours">
          <h6 className="scheduled-title">
            <FontAwesomeIcon icon={faClock} className="title-icon" />
            Scheduled Hours of Play:
          </h6>

          <div className="schedule-item">
            <span className="schedule-label">First Innings:</span>
            <span className="schedule-value">
              {scheduledHours.firstInnings}
            </span>
          </div>

          <div className="schedule-item">
            <span className="schedule-label">Length of Interval:</span>
            <span className="schedule-value">20 minutes</span>
          </div>

          <div className="schedule-item">
            <span className="schedule-label">Second Innings:</span>
            <span className="schedule-value">
              {scheduledHours.secondInnings}
            </span>
          </div>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          className={`submit-btn ${isValid ? "valid" : "invalid"}`}
          disabled={!isValid}
        >
          Save Match Information
        </button>
      </form>
    </div>
  );
};

export default MatchInformation;
