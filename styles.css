/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
}

.container-fluid {
    padding: 0;
    height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    background-color: #ffffff;
    border-right: 1px solid #dee2e6;
    padding: 0;
    height: 100vh;
    overflow-y: auto;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.accordion {
    border: none;
}

.accordion-item {
    border: none;
    border-bottom: 1px solid #dee2e6;
}

.accordion-button {
    background-color: #ffffff;
    border: none;
    padding: 1rem 1.25rem;
    font-weight: 500;
    color: #495057;
    transition: all 0.3s ease;
}

.accordion-button:not(.collapsed) {
    background-color: #e3f2fd;
    color: #1976d2;
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(25, 118, 210, 0.25);
    border-color: #1976d2;
}

.accordion-button::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23495057'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.accordion-body {
    padding: 0.5rem 1.25rem 1rem;
}

.nav-link {
    color: #6c757d;
    text-decoration: none;
    padding: 0.5rem 0;
    display: block;
    cursor: pointer;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #1976d2;
}

.nav-link.active {
    color: #1976d2;
    font-weight: 500;
}

/* Main Content Styles */
.main-content {
    padding: 2rem;
    height: 100vh;
    overflow-y: auto;
    background-color: #f8f9fa;
}

.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 10px;
    margin-bottom: 2rem;
}

.card-header {
    background-color: #ffffff;
    border-bottom: 2px solid #e3f2fd;
    padding: 1.5rem;
    border-radius: 10px 10px 0 0 !important;
}

.card-header h4 {
    margin: 0;
    color: #1976d2;
    font-weight: 600;
}

.card-body {
    padding: 2rem;
    background-color: #ffffff;
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Form Styles */
.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #1976d2;
    box-shadow: 0 0 0 0.25rem rgba(25, 118, 210, 0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: block;
    font-size: 0.875rem;
    color: #dc3545;
    margin-top: 0.25rem;
}

/* Input Group Styles */
.input-group .btn {
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.input-group .btn:hover {
    background-color: #1976d2;
    border-color: #1976d2;
    color: white;
}

.input-group .form-control {
    border-left: none;
    border-right: none;
}

/* Scheduled Hours Styles */
.scheduled-hours {
    background-color: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border: 2px solid #e3f2fd;
}

.scheduled-hours h5 {
    border-bottom: 2px solid #1976d2;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

.scheduled-hours .row {
    margin-bottom: 0.5rem;
}

.scheduled-hours .row:last-child {
    margin-bottom: 0;
}

/* Text Colors */
.text-primary {
    color: #1976d2 !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        height: auto;
        position: relative;
    }
    
    .main-content {
        padding: 1rem;
        height: auto;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .scheduled-hours {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .accordion-button {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
    
    .card-header {
        padding: 1rem;
    }
    
    .card-header h4 {
        font-size: 1.1rem;
    }
    
    .scheduled-hours .col-sm-4,
    .scheduled-hours .col-sm-8 {
        margin-bottom: 0.25rem;
    }
}

/* Accessibility Improvements */
.accordion-button:focus,
.nav-link:focus,
.form-control:focus,
.btn:focus {
    outline: 2px solid #1976d2;
    outline-offset: 2px;
}

/* Loading Animation */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Success/Error States */
.alert {
    border-radius: 8px;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
}
