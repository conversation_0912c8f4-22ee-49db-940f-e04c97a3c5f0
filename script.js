// Cricket Match Management Application
class CricketMatchManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.calculateScheduledHours();
        this.setupFormValidation();
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Time-outs counter buttons
        document.getElementById('decrementTimeouts').addEventListener('click', () => {
            this.updateTimeouts(-1);
        });

        document.getElementById('incrementTimeouts').addEventListener('click', () => {
            this.updateTimeouts(1);
        });

        // Start time change listener
        document.getElementById('startTime').addEventListener('change', () => {
            this.calculateScheduledHours();
        });

        // Form submission
        document.getElementById('matchForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.validateAndSubmitForm();
        });

        // Real-time validation
        document.getElementById('teams').addEventListener('input', () => {
            this.validateTeamsField();
        });
    }

    switchTab(tabId) {
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // Remove active class from all nav links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // Show selected tab content
        const targetTab = document.getElementById(tabId);
        if (targetTab) {
            targetTab.classList.add('active');
        }

        // Add active class to clicked nav link
        const activeLink = document.querySelector(`[data-tab="${tabId}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }

    updateTimeouts(change) {
        const timeoutsInput = document.getElementById('timeouts');
        let currentValue = parseInt(timeoutsInput.value);
        let newValue = currentValue + change;

        // Ensure value stays within bounds (0-4)
        if (newValue >= 0 && newValue <= 4) {
            timeoutsInput.value = newValue;
            
            // Add visual feedback
            timeoutsInput.classList.add('updated');
            setTimeout(() => {
                timeoutsInput.classList.remove('updated');
            }, 300);
        }
    }

    calculateScheduledHours() {
        const startTimeInput = document.getElementById('startTime');
        const startTime = startTimeInput.value;

        if (!startTime) return;

        try {
            // Parse start time
            const [hours, minutes] = startTime.split(':').map(Number);
            const startDate = new Date();
            startDate.setHours(hours, minutes, 0, 0);

            // Calculate first innings end (start + 90 minutes)
            const firstInningsEnd = new Date(startDate.getTime() + 90 * 60000);

            // Calculate interval end (first innings end + 20 minutes)
            const intervalEnd = new Date(firstInningsEnd.getTime() + 20 * 60000);

            // Calculate second innings end (interval end + 90 minutes)
            const secondInningsEnd = new Date(intervalEnd.getTime() + 90 * 60000);

            // Format times
            const formatTime = (date) => {
                return date.toTimeString().slice(0, 5);
            };

            // Update display
            document.getElementById('firstInningsTime').textContent = 
                `${formatTime(startDate)} - ${formatTime(firstInningsEnd)}`;
            
            document.getElementById('secondInningsTime').textContent = 
                `${formatTime(intervalEnd)} - ${formatTime(secondInningsEnd)}`;

        } catch (error) {
            console.error('Error calculating scheduled hours:', error);
            // Reset to default values if calculation fails
            document.getElementById('firstInningsTime').textContent = '-- : -- - -- : --';
            document.getElementById('secondInningsTime').textContent = '-- : -- - -- : --';
        }
    }

    setupFormValidation() {
        const form = document.getElementById('matchForm');
        
        // Bootstrap validation classes
        form.classList.add('needs-validation');
    }

    validateTeamsField() {
        const teamsInput = document.getElementById('teams');
        const value = teamsInput.value.trim();

        if (value.length === 0) {
            teamsInput.classList.add('is-invalid');
            teamsInput.classList.remove('is-valid');
            return false;
        } else if (value.length < 3) {
            teamsInput.classList.add('is-invalid');
            teamsInput.classList.remove('is-valid');
            teamsInput.nextElementSibling.textContent = 'Team names must be at least 3 characters long.';
            return false;
        } else {
            teamsInput.classList.remove('is-invalid');
            teamsInput.classList.add('is-valid');
            return true;
        }
    }

    validateAndSubmitForm() {
        const form = document.getElementById('matchForm');
        let isValid = true;

        // Validate teams field
        if (!this.validateTeamsField()) {
            isValid = false;
        }

        // Validate start time
        const startTimeInput = document.getElementById('startTime');
        if (!startTimeInput.value) {
            startTimeInput.classList.add('is-invalid');
            isValid = false;
        } else {
            startTimeInput.classList.remove('is-invalid');
            startTimeInput.classList.add('is-valid');
        }

        if (isValid) {
            this.submitForm();
        } else {
            // Show error message
            this.showAlert('Please fill in all required fields correctly.', 'danger');
        }
    }

    submitForm() {
        const formData = {
            teams: document.getElementById('teams').value,
            timeouts: document.getElementById('timeouts').value,
            startTime: document.getElementById('startTime').value
        };

        // Simulate form submission
        this.showAlert('Match information saved successfully!', 'success');
        console.log('Form submitted with data:', formData);
    }

    showAlert(message, type) {
        // Remove existing alerts
        const existingAlerts = document.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        // Create new alert
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        // Insert alert at the top of the card body
        const cardBody = document.querySelector('#match-info .card-body');
        cardBody.insertBefore(alert, cardBody.firstChild);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }

    // Utility method to handle midnight crossover
    handleMidnightCrossover(date) {
        // If the calculated time goes past midnight, handle appropriately
        // This is a placeholder for more complex logic if needed
        return date;
    }

    // Method to export match data
    exportMatchData() {
        const data = {
            teams: document.getElementById('teams').value,
            timeouts: document.getElementById('timeouts').value,
            startTime: document.getElementById('startTime').value,
            firstInnings: document.getElementById('firstInningsTime').textContent,
            secondInnings: document.getElementById('secondInningsTime').textContent,
            timestamp: new Date().toISOString()
        };

        return data;
    }

    // Method to import match data
    importMatchData(data) {
        if (data.teams) document.getElementById('teams').value = data.teams;
        if (data.timeouts) document.getElementById('timeouts').value = data.timeouts;
        if (data.startTime) {
            document.getElementById('startTime').value = data.startTime;
            this.calculateScheduledHours();
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.cricketApp = new CricketMatchManager();
});

// Add CSS class for updated animation
const style = document.createElement('style');
style.textContent = `
    .updated {
        background-color: #e3f2fd !important;
        transition: background-color 0.3s ease;
    }
    
    .is-valid {
        border-color: #28a745 !important;
    }
    
    .is-invalid {
        border-color: #dc3545 !important;
    }
`;
document.head.appendChild(style);

// Keyboard accessibility improvements
document.addEventListener('keydown', (e) => {
    // Allow Enter key to activate accordion buttons
    if (e.key === 'Enter' && e.target.classList.contains('accordion-button')) {
        e.target.click();
    }
    
    // Allow Enter key to activate nav links
    if (e.key === 'Enter' && e.target.classList.contains('nav-link')) {
        e.target.click();
    }
});

// Add ARIA labels for better accessibility
document.addEventListener('DOMContentLoaded', () => {
    // Add ARIA labels to increment/decrement buttons
    document.getElementById('decrementTimeouts').setAttribute('aria-label', 'Decrease timeouts');
    document.getElementById('incrementTimeouts').setAttribute('aria-label', 'Increase timeouts');
    
    // Add ARIA descriptions to form fields
    document.getElementById('teams').setAttribute('aria-describedby', 'teams-help');
    document.getElementById('timeouts').setAttribute('aria-describedby', 'timeouts-help');
    document.getElementById('startTime').setAttribute('aria-describedby', 'starttime-help');
});
