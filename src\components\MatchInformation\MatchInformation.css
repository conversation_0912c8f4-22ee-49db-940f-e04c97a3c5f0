/* Match Information Container */
.match-information {
  width: 100%;
  max-width: 100%;
}

.match-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Form Group */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Form Labels */
.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.label-icon {
  color: #3b82f6;
  font-size: 0.875rem;
}

.required {
  color: #ef4444;
  margin-left: 0.25rem;
}

/* Form Controls */
.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: #ffffff;
  color: #374151;
}

.form-control:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control.is-valid {
  border-color: #10b981;
}

.form-control.is-invalid {
  border-color: #ef4444;
}

.time-input {
  max-width: 150px;
}

/* Timeout Counter */
.timeout-counter {
  display: flex;
  align-items: center;
  gap: 0;
  max-width: 140px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  background: #ffffff;
}

.counter-btn {
  width: 36px;
  height: 40px;
  border: none;
  background: #f9fafb;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.counter-btn:hover:not(:disabled) {
  background: #3b82f6;
  color: #ffffff;
}

.counter-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.counter-btn.decrement {
  border-right: 1px solid #e5e7eb;
}

.counter-btn.increment {
  border-left: 1px solid #e5e7eb;
}

.counter-input {
  width: 60px;
  height: 40px;
  border: none;
  text-align: center;
  font-weight: 600;
  color: #374151;
  background: #ffffff;
  font-size: 0.875rem;
}

.counter-input:focus {
  outline: none;
}

/* Form Text */
.form-text {
  color: #6b7280;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Error Messages */
.error-message {
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* Scheduled Hours */
.scheduled-hours {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 2px solid #bae6fd;
  border-radius: 12px;
  padding: 1.25rem;
  margin-top: 0.5rem;
}

.scheduled-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #0369a1;
  font-weight: 600;
  font-size: 0.875rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #0ea5e9;
}

.title-icon {
  color: #0ea5e9;
}

.schedule-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(14, 165, 233, 0.2);
}

.schedule-item:last-child {
  border-bottom: none;
}

.schedule-label {
  font-weight: 600;
  color: #0369a1;
  font-size: 0.8rem;
}

.schedule-value {
  font-weight: 500;
  color: #0c4a6e;
  font-size: 0.8rem;
  font-family: 'Courier New', monospace;
}

/* Submit Button */
.submit-btn {
  width: 100%;
  padding: 0.875rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 0.5rem;
}

.submit-btn.valid {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.submit-btn.valid:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.submit-btn.invalid {
  background: #f3f4f6;
  color: #9ca3af;
  cursor: not-allowed;
}

.submit-btn:disabled {
  cursor: not-allowed;
}

/* Animations */
.form-group {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .match-form {
    gap: 1.25rem;
  }
  
  .form-control {
    padding: 0.625rem;
    font-size: 0.8rem;
  }
  
  .timeout-counter {
    max-width: 120px;
  }
  
  .counter-btn {
    width: 32px;
    height: 36px;
    font-size: 0.8rem;
  }
  
  .counter-input {
    width: 50px;
    height: 36px;
    font-size: 0.8rem;
  }
  
  .scheduled-hours {
    padding: 1rem;
  }
  
  .schedule-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .submit-btn {
    padding: 0.75rem 1.25rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 576px) {
  .match-form {
    gap: 1rem;
  }
  
  .form-label {
    font-size: 0.8rem;
  }
  
  .form-control {
    padding: 0.5rem;
    font-size: 0.75rem;
  }
  
  .timeout-counter {
    max-width: 110px;
  }
  
  .counter-btn {
    width: 28px;
    height: 32px;
    font-size: 0.75rem;
  }
  
  .counter-input {
    width: 45px;
    height: 32px;
    font-size: 0.75rem;
  }
  
  .scheduled-title {
    font-size: 0.8rem;
  }
  
  .schedule-label,
  .schedule-value {
    font-size: 0.75rem;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .form-control {
    border-width: 3px;
  }
  
  .form-control:focus {
    border-color: #000000;
  }
  
  .timeout-counter {
    border-width: 3px;
  }
  
  .scheduled-hours {
    border-width: 3px;
    border-color: #000000;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .form-control,
  .counter-btn,
  .submit-btn,
  .form-group {
    transition: none;
    animation: none;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .form-label {
    color: #e5e7eb;
  }
  
  .form-control {
    background: #374151;
    border-color: #4b5563;
    color: #f9fafb;
  }
  
  .form-control:focus {
    border-color: #60a5fa;
  }
  
  .timeout-counter {
    border-color: #4b5563;
    background: #374151;
  }
  
  .counter-btn {
    background: #4b5563;
    color: #d1d5db;
  }
  
  .counter-btn:hover:not(:disabled) {
    background: #60a5fa;
  }
  
  .counter-input {
    background: #374151;
    color: #f9fafb;
  }
  
  .scheduled-hours {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    border-color: #3b82f6;
  }
  
  .scheduled-title {
    color: #dbeafe;
    border-bottom-color: #60a5fa;
  }
  
  .schedule-label {
    color: #bfdbfe;
  }
  
  .schedule-value {
    color: #dbeafe;
  }
  
  .error-message {
    color: #fca5a5;
  }
}
