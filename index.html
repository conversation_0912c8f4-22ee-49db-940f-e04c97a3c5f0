<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cricket Match Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar Navigation -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="accordion" id="sidebarAccordion">
                    <!-- Match Information Tab -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="matchInfoHeader">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#matchInfoCollapse" aria-expanded="true" 
                                    aria-controls="matchInfoCollapse">
                                <i class="fas fa-info-circle me-2"></i>
                                Match Information
                            </button>
                        </h2>
                        <div id="matchInfoCollapse" class="accordion-collapse collapse show" 
                             aria-labelledby="matchInfoHeader" data-bs-parent="#sidebarAccordion">
                            <div class="accordion-body">
                                <span class="nav-link active" data-tab="match-info">View Details</span>
                            </div>
                        </div>
                    </div>

                    <!-- Cut-off Time Tab -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="cutoffTimeHeader">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#cutoffTimeCollapse" aria-expanded="false" 
                                    aria-controls="cutoffTimeCollapse">
                                <i class="fas fa-clock me-2"></i>
                                Cut-off Time
                            </button>
                        </h2>
                        <div id="cutoffTimeCollapse" class="accordion-collapse collapse" 
                             aria-labelledby="cutoffTimeHeader" data-bs-parent="#sidebarAccordion">
                            <div class="accordion-body">
                                <span class="nav-link" data-tab="cutoff-time">Manage Cut-off</span>
                            </div>
                        </div>
                    </div>

                    <!-- Delayed Start Tab -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="delayedStartHeader">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#delayedStartCollapse" aria-expanded="false" 
                                    aria-controls="delayedStartCollapse">
                                <i class="fas fa-pause-circle me-2"></i>
                                Delayed Start
                            </button>
                        </h2>
                        <div id="delayedStartCollapse" class="accordion-collapse collapse" 
                             aria-labelledby="delayedStartHeader" data-bs-parent="#sidebarAccordion">
                            <div class="accordion-body">
                                <span class="nav-link" data-tab="delayed-start">Manage Delays</span>
                            </div>
                        </div>
                    </div>

                    <!-- Interruption: 1st Innings Tab -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="interruption1Header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#interruption1Collapse" aria-expanded="false" 
                                    aria-controls="interruption1Collapse">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Interruption: 1st Innings
                            </button>
                        </h2>
                        <div id="interruption1Collapse" class="accordion-collapse collapse" 
                             aria-labelledby="interruption1Header" data-bs-parent="#sidebarAccordion">
                            <div class="accordion-body">
                                <span class="nav-link" data-tab="interruption-1st">Manage 1st Innings</span>
                            </div>
                        </div>
                    </div>

                    <!-- Interruption: 2nd Innings Tab -->
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="interruption2Header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#interruption2Collapse" aria-expanded="false" 
                                    aria-controls="interruption2Collapse">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Interruption: 2nd Innings
                            </button>
                        </h2>
                        <div id="interruption2Collapse" class="accordion-collapse collapse" 
                             aria-labelledby="interruption2Header" data-bs-parent="#sidebarAccordion">
                            <div class="accordion-body">
                                <span class="nav-link" data-tab="interruption-2nd">Manage 2nd Innings</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Match Information Content -->
                <div id="match-info" class="tab-content active">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-info-circle me-2"></i>Match Information</h4>
                        </div>
                        <div class="card-body">
                            <form id="matchForm" novalidate>
                                <!-- Teams Input -->
                                <div class="mb-3">
                                    <label for="teams" class="form-label">Teams for the match <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="teams" name="teams" 
                                           placeholder="e.g., AB vs CD" required>
                                    <div class="invalid-feedback">
                                        Please enter the teams for the match.
                                    </div>
                                </div>

                                <!-- Time Outs Counter -->
                                <div class="mb-3">
                                    <label for="timeouts" class="form-label">Number of Time Outs taken:</label>
                                    <div class="input-group" style="max-width: 200px;">
                                        <button class="btn btn-outline-secondary" type="button" id="decrementTimeouts">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input type="number" class="form-control text-center" id="timeouts" 
                                               name="timeouts" min="0" max="4" value="0" readonly>
                                        <button class="btn btn-outline-secondary" type="button" id="incrementTimeouts">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Match Start Time -->
                                <div class="mb-4">
                                    <label for="startTime" class="form-label">Start Time of the match:</label>
                                    <input type="time" class="form-control" id="startTime" name="startTime" 
                                           style="max-width: 200px;" value="15:20">
                                </div>

                                <!-- Scheduled Hours of Play -->
                                <div class="scheduled-hours">
                                    <h5 class="mb-3 text-primary">Scheduled Hours of Play:</h5>
                                    
                                    <div class="row mb-2">
                                        <div class="col-sm-4">
                                            <strong class="text-primary">First Innings:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            <span id="firstInningsTime">15:20 - 16:50</span>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-2">
                                        <div class="col-sm-4">
                                            <strong class="text-primary">Length of Interval:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            <span>20 minutes</span>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-sm-4">
                                            <strong class="text-primary">Second Innings:</strong>
                                        </div>
                                        <div class="col-sm-8">
                                            <span id="secondInningsTime">17:10 - 18:40</span>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Other Tab Contents (Placeholders) -->
                <div id="cutoff-time" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-clock me-2"></i>Cut-off Time Management</h4>
                        </div>
                        <div class="card-body">
                            <p>Cut-off time management features will be implemented here.</p>
                        </div>
                    </div>
                </div>

                <div id="delayed-start" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-pause-circle me-2"></i>Delayed Start Management</h4>
                        </div>
                        <div class="card-body">
                            <p>Delayed start management features will be implemented here.</p>
                        </div>
                    </div>
                </div>

                <div id="interruption-1st" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-exclamation-triangle me-2"></i>1st Innings Interruption</h4>
                        </div>
                        <div class="card-body">
                            <p>1st innings interruption management features will be implemented here.</p>
                        </div>
                    </div>
                </div>

                <div id="interruption-2nd" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <h4><i class="fas fa-exclamation-triangle me-2"></i>2nd Innings Interruption</h4>
                        </div>
                        <div class="card-body">
                            <p>2nd innings interruption management features will be implemented here.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
