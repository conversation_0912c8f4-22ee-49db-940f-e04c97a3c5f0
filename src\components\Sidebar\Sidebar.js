import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faInfoCircle, 
  faClock, 
  faPauseCircle, 
  faExclamationTriangle,
  faChevronLeft,
  faChevronRight,
  faBars
} from '@fortawesome/free-solid-svg-icons';
import { Tooltip } from 'react-tooltip';
import NavigationItem from './NavigationItem';
import MatchInformation from '../MatchInformation/MatchInformation';
import './Sidebar.css';

const Sidebar = ({ isCollapsed, onToggle }) => {
  const [activeTab, setActiveTab] = useState('match-info');
  const [expandedAccordion, setExpandedAccordion] = useState('match-info');

  const navigationItems = [
    {
      id: 'match-info',
      icon: faInfoCircle,
      label: 'Match Information',
      component: MatchInformation
    },
    {
      id: 'cutoff-time',
      icon: faClock,
      label: 'Cut-off Time',
      component: () => <div className="placeholder-content">Cut-off Time Management</div>
    },
    {
      id: 'delayed-start',
      icon: faPauseCircle,
      label: 'Delayed Start',
      component: () => <div className="placeholder-content">Delayed Start Management</div>
    },
    {
      id: 'interruption-1st',
      icon: faExclamationTriangle,
      label: 'Interruption: 1st Innings',
      component: () => <div className="placeholder-content">1st Innings Interruption</div>
    },
    {
      id: 'interruption-2nd',
      icon: faExclamationTriangle,
      label: 'Interruption: 2nd Innings',
      component: () => <div className="placeholder-content">2nd Innings Interruption</div>
    }
  ];

  const handleAccordionToggle = (itemId) => {
    if (isCollapsed) {
      // If collapsed, just set active tab
      setActiveTab(itemId);
      setExpandedAccordion(itemId);
    } else {
      // If expanded, toggle accordion
      setExpandedAccordion(expandedAccordion === itemId ? null : itemId);
      setActiveTab(itemId);
    }
  };

  const ActiveComponent = navigationItems.find(item => item.id === activeTab)?.component;

  return (
    <>
      <div className={`sidebar ${isCollapsed ? 'collapsed' : 'expanded'}`}>
        {/* Header with toggle button */}
        <div className="sidebar-header">
          <div className="header-content">
            {!isCollapsed && (
              <div className="header-title">
                <FontAwesomeIcon icon={faInfoCircle} className="header-icon" />
                <span>Cricket Match</span>
              </div>
            )}
            <button 
              className="toggle-btn"
              onClick={onToggle}
              aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
              data-tooltip-id="toggle-tooltip"
              data-tooltip-content={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
            >
              <FontAwesomeIcon 
                icon={isCollapsed ? faChevronRight : faChevronLeft} 
              />
            </button>
          </div>
        </div>

        {/* Navigation Items */}
        <div className="sidebar-nav">
          {navigationItems.map((item) => (
            <NavigationItem
              key={item.id}
              item={item}
              isCollapsed={isCollapsed}
              isActive={activeTab === item.id}
              isExpanded={expandedAccordion === item.id}
              onToggle={() => handleAccordionToggle(item.id)}
            />
          ))}
        </div>

        {/* Footer */}
        <div className="sidebar-footer">
          {!isCollapsed && (
            <div className="footer-content">
              <small className="text-muted">Cricket Match Management v1.0</small>
            </div>
          )}
        </div>
      </div>

      {/* Content Panel for collapsed state */}
      {isCollapsed && activeTab && (
        <div className="content-panel">
          <div className="content-header">
            <h4>
              <FontAwesomeIcon 
                icon={navigationItems.find(item => item.id === activeTab)?.icon} 
                className="me-2"
              />
              {navigationItems.find(item => item.id === activeTab)?.label}
            </h4>
          </div>
          <div className="content-body">
            {ActiveComponent && <ActiveComponent />}
          </div>
        </div>
      )}

      {/* Tooltips */}
      <Tooltip 
        id="toggle-tooltip" 
        place="right"
        className="custom-tooltip"
      />
      <Tooltip 
        id="nav-tooltip" 
        place="right"
        className="custom-tooltip"
      />
    </>
  );
};

export default Sidebar;
